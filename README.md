# InnoSTT - 粤语语音转文字应用

## 项目简介

InnoSTT是一个专为粤语语音识别优化的Android应用，支持实时语音转文字功能，具有现代化的UI设计和高性能的文本显示能力。

## 主要功能

### 🎤 粤语语音识别
- 基于Microsoft Azure Speech SDK
- 专门配置为粤语识别（zh-HK）
- 支持实时语音转文字
- 区分部分识别结果和最终结果

### 📊 实时音频波形图
- 自定义AudioWaveformView显示实时音频波形
- 录音时显示波形动画，停止时隐藏
- 波形图反映实际音频输入的音量变化
- 平滑的动画效果

### 📱 现代化UI设计
- **顶部**：实时音频波形图显示区域
- **中间**：RecyclerView文本显示区域，支持流畅滚动
- **底部**：圆形麦克风录音按钮（Material Design）

### ⚡ 高性能优化
- 使用RecyclerView替代TextView，支持长文本高效显示
- ViewHolder模式和DiffUtil优化列表性能
- 智能内存管理，支持10000+字符处理
- 自动滚动到底部的平滑动画

## 技术架构

### 核心组件
- **MainActivity**: 主界面控制器
- **AzureSpeech**: 语音识别服务封装
- **SpeechResultManager**: 识别结果数据管理
- **RecognitionResultAdapter**: RecyclerView适配器
- **AudioWaveformView**: 自定义音频波形图视图
- **AudioLevelMonitor**: 音频级别实时监测

### 数据流
```
麦克风输入 → Azure Speech SDK → 识别结果 → SpeechResultManager → RecyclerView显示
     ↓
AudioLevelMonitor → AudioWaveformView → 波形图显示
```

## 性能特性

### 内存优化
- 最大缓存1000个识别结果项
- 自动清理过旧的数据
- 防止内存泄漏的资源管理

### UI性能
- RecyclerView视图复用机制
- DiffUtil高效数据更新
- 平滑滚动动画
- 60FPS波形图动画

### 长文本支持
- 支持10000+字符显示
- 高效的文本渲染
- 流畅的滚动体验

## 权限要求

- `RECORD_AUDIO`: 录音权限，用于语音识别和音频级别监测

## 构建要求

- Android SDK 28+
- Java 8+
- Microsoft Azure Speech SDK 1.33.0

## 使用说明

1. **启动应用**：打开应用后会看到三层布局界面
2. **授权权限**：首次使用需要授予录音权限
3. **开始识别**：点击底部红色麦克风按钮开始语音识别
4. **查看结果**：
   - 顶部显示实时音频波形图
   - 中间区域显示识别的文字结果
   - 支持手动滚动查看历史内容
5. **停止识别**：再次点击麦克风按钮停止识别

## 开发日志

### 已完成改进 ✅
- [x] 架构重构：TextView → RecyclerView
- [x] 数据管理层实现
- [x] UI布局重构（三层设计）
- [x] 音频波形图功能
- [x] 性能优化
- [x] 语音识别改进

### 待优化项目
- [ ] 网络状态检测
- [ ] 自动重连机制
- [ ] 全局异常处理
- [ ] 用户引导优化

## 技术亮点

1. **高性能文本显示**：使用RecyclerView + ViewHolder + DiffUtil实现高效的长文本显示
2. **实时音频可视化**：自定义View实现的音频波形图，提供直观的音频反馈
3. **智能内存管理**：防止内存泄漏的数据管理机制
4. **现代化UI设计**：Material Design风格的界面设计
5. **优化的语音识别**：区分部分结果和最终结果，提供更好的用户体验

## 许可证

本项目仅供学习和研究使用。
