package com.example.speechtotext;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

/**
 * 音频级别监测器
 * 用于实时监测麦克风输入的音频级别，为波形图提供数据
 */
public class AudioLevelMonitor {
    
    private static final String TAG = "AudioLevelMonitor";
    
    // 音频录制参数
    private static final int SAMPLE_RATE = 44100;
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    
    private AudioRecord audioRecord;
    private boolean isMonitoring = false;
    private Thread monitoringThread;
    
    private AudioLevelListener listener;
    
    // 缓冲区大小
    private int bufferSize;
    private short[] audioBuffer;
    
    public interface AudioLevelListener {
        void onAudioLevelChanged(float level); // level: 0-100
    }
    
    public AudioLevelMonitor() {
        initializeAudioRecord();
    }
    
    private void initializeAudioRecord() {
        try {
            // 计算缓冲区大小
            bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Log.e(TAG, "Invalid buffer size: " + bufferSize);
                return;
            }
            
            // 创建AudioRecord实例
            audioRecord = new AudioRecord(
                    MediaRecorder.AudioSource.MIC,
                    SAMPLE_RATE,
                    CHANNEL_CONFIG,
                    AUDIO_FORMAT,
                    bufferSize
            );
            
            // 初始化音频缓冲区
            audioBuffer = new short[bufferSize];
            
            Log.d(TAG, "AudioRecord initialized successfully. Buffer size: " + bufferSize);
            
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied for audio recording", e);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize AudioRecord", e);
        }
    }
    
    /**
     * 开始监测音频级别
     */
    public void startMonitoring() {
        if (audioRecord == null) {
            Log.e(TAG, "AudioRecord not initialized");
            return;
        }
        
        if (isMonitoring) {
            Log.w(TAG, "Already monitoring");
            return;
        }
        
        try {
            audioRecord.startRecording();
            isMonitoring = true;
            
            // 启动监测线程
            monitoringThread = new Thread(this::monitorAudioLevel);
            monitoringThread.start();
            
            Log.d(TAG, "Audio level monitoring started");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to start audio monitoring", e);
            isMonitoring = false;
        }
    }
    
    /**
     * 停止监测音频级别
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }
        
        isMonitoring = false;
        
        try {
            if (audioRecord != null && audioRecord.getState() == AudioRecord.STATE_INITIALIZED) {
                audioRecord.stop();
            }
            
            // 等待监测线程结束
            if (monitoringThread != null) {
                monitoringThread.interrupt();
                monitoringThread = null;
            }
            
            Log.d(TAG, "Audio level monitoring stopped");
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping audio monitoring", e);
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopMonitoring();
        
        if (audioRecord != null) {
            try {
                audioRecord.release();
                audioRecord = null;
                Log.d(TAG, "AudioRecord released");
            } catch (Exception e) {
                Log.e(TAG, "Error releasing AudioRecord", e);
            }
        }
    }
    
    /**
     * 监测音频级别的主循环
     */
    private void monitorAudioLevel() {
        while (isMonitoring && !Thread.currentThread().isInterrupted()) {
            try {
                // 读取音频数据
                int readResult = audioRecord.read(audioBuffer, 0, bufferSize);
                
                if (readResult > 0) {
                    // 计算音频级别
                    float level = calculateAudioLevel(audioBuffer, readResult);
                    
                    // 通知监听器
                    if (listener != null) {
                        listener.onAudioLevelChanged(level);
                    }
                }
                
                // 短暂休眠以控制更新频率
                Thread.sleep(50); // 20 FPS
                
            } catch (InterruptedException e) {
                Log.d(TAG, "Audio monitoring thread interrupted");
                break;
            } catch (Exception e) {
                Log.e(TAG, "Error in audio monitoring loop", e);
                break;
            }
        }
    }
    
    /**
     * 计算音频级别
     * @param audioData 音频数据
     * @param length 数据长度
     * @return 音频级别 (0-100)
     */
    private float calculateAudioLevel(short[] audioData, int length) {
        if (audioData == null || length <= 0) {
            return 0f;
        }
        
        // 计算RMS (Root Mean Square)
        long sum = 0;
        for (int i = 0; i < length; i++) {
            sum += audioData[i] * audioData[i];
        }
        
        double rms = Math.sqrt((double) sum / length);
        
        // 将RMS值映射到0-100范围
        // 这里的映射可能需要根据实际情况调整
        float level = (float) (rms / 32767.0 * 100.0);
        
        // 限制范围
        level = Math.max(0f, Math.min(100f, level));
        
        // 应用对数缩放以获得更好的视觉效果
        if (level > 0) {
            level = (float) (Math.log10(level + 1) / Math.log10(101) * 100);
        }
        
        return level;
    }
    
    /**
     * 设置音频级别监听器
     */
    public void setAudioLevelListener(AudioLevelListener listener) {
        this.listener = listener;
    }
    
    /**
     * 检查是否正在监测
     */
    public boolean isMonitoring() {
        return isMonitoring;
    }
    
    /**
     * 检查AudioRecord是否可用
     */
    public boolean isAudioRecordAvailable() {
        return audioRecord != null && audioRecord.getState() == AudioRecord.STATE_INITIALIZED;
    }
}
