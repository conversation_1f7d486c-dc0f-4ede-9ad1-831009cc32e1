package com.example.speechtotext;

/**
 * 语音识别结果数据模型
 * 用于RecyclerView显示的单个文本项
 */
public class SpeechResultItem {
    private String text;
    private long timestamp;
    private boolean isPartial; // 是否为部分识别结果（实时识别中）
    private int id;

    public SpeechResultItem(int id, String text, long timestamp, boolean isPartial) {
        this.id = id;
        this.text = text;
        this.timestamp = timestamp;
        this.isPartial = isPartial;
    }

    // Getters
    public String getText() {
        return text;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public boolean isPartial() {
        return isPartial;
    }

    public int getId() {
        return id;
    }

    // Setters
    public void setText(String text) {
        this.text = text;
    }

    public void setPartial(boolean partial) {
        isPartial = partial;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SpeechResultItem that = (SpeechResultItem) obj;
        return id == that.id &&
                timestamp == that.timestamp &&
                isPartial == that.isPartial &&
                (text != null ? text.equals(that.text) : that.text == null);
    }

    @Override
    public int hashCode() {
        int result = text != null ? text.hashCode() : 0;
        result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
        result = 31 * result + (isPartial ? 1 : 0);
        result = 31 * result + id;
        return result;
    }

    @Override
    public String toString() {
        return "SpeechResultItem{" +
                "id=" + id +
                ", text='" + text + '\'' +
                ", timestamp=" + timestamp +
                ", isPartial=" + isPartial +
                '}';
    }
}
