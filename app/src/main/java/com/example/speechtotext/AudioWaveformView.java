package com.example.speechtotext;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 自定义音频波形图视图
 * 显示实时音频输入的波形效果
 */
public class AudioWaveformView extends View {
    
    private static final int MAX_AMPLITUDE_HISTORY = 50; // 保存的振幅历史数量
    private static final int DEFAULT_AMPLITUDE = 10; // 默认振幅
    private static final int MAX_AMPLITUDE = 100; // 最大振幅
    
    private Paint waveformPaint;
    private Paint backgroundPaint;
    private Path waveformPath;
    
    private List<Float> amplitudeHistory;
    private float currentAmplitude = 0f;
    private boolean isAnimating = false;
    
    // 动画相关
    private long lastUpdateTime = 0;
    private static final long UPDATE_INTERVAL = 50; // 更新间隔（毫秒）
    
    // 颜色配置
    private int waveformColor = Color.parseColor("#E53E3E");
    private int backgroundColor = Color.TRANSPARENT;
    
    public AudioWaveformView(Context context) {
        super(context);
        init();
    }
    
    public AudioWaveformView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public AudioWaveformView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 初始化画笔
        waveformPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        waveformPaint.setColor(waveformColor);
        waveformPaint.setStrokeWidth(3f);
        waveformPaint.setStyle(Paint.Style.STROKE);
        
        backgroundPaint = new Paint();
        backgroundPaint.setColor(backgroundColor);
        
        waveformPath = new Path();
        amplitudeHistory = new ArrayList<>();
        
        // 初始化振幅历史
        for (int i = 0; i < MAX_AMPLITUDE_HISTORY; i++) {
            amplitudeHistory.add(0f);
        }
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        int width = getWidth();
        int height = getHeight();
        
        if (width <= 0 || height <= 0) {
            return;
        }
        
        // 绘制背景
        canvas.drawRect(0, 0, width, height, backgroundPaint);
        
        // 绘制波形
        drawWaveform(canvas, width, height);
    }
    
    private void drawWaveform(Canvas canvas, int width, int height) {
        if (amplitudeHistory.isEmpty()) {
            return;
        }
        
        waveformPath.reset();
        
        float centerY = height / 2f;
        float stepX = (float) width / (MAX_AMPLITUDE_HISTORY - 1);
        
        // 创建波形路径
        for (int i = 0; i < amplitudeHistory.size(); i++) {
            float x = i * stepX;
            float amplitude = amplitudeHistory.get(i);
            
            // 将振幅映射到视图高度
            float normalizedAmplitude = (amplitude / MAX_AMPLITUDE) * (height / 2f - 10);
            
            if (i == 0) {
                waveformPath.moveTo(x, centerY - normalizedAmplitude);
            } else {
                waveformPath.lineTo(x, centerY - normalizedAmplitude);
            }
        }
        
        // 绘制上半部分波形
        canvas.drawPath(waveformPath, waveformPaint);
        
        // 绘制下半部分波形（镜像）
        waveformPath.reset();
        for (int i = 0; i < amplitudeHistory.size(); i++) {
            float x = i * stepX;
            float amplitude = amplitudeHistory.get(i);
            float normalizedAmplitude = (amplitude / MAX_AMPLITUDE) * (height / 2f - 10);
            
            if (i == 0) {
                waveformPath.moveTo(x, centerY + normalizedAmplitude);
            } else {
                waveformPath.lineTo(x, centerY + normalizedAmplitude);
            }
        }
        canvas.drawPath(waveformPath, waveformPaint);
        
        // 绘制中心线
        Paint centerLinePaint = new Paint();
        centerLinePaint.setColor(Color.parseColor("#66FFFFFF"));
        centerLinePaint.setStrokeWidth(1f);
        canvas.drawLine(0, centerY, width, centerY, centerLinePaint);
    }
    
    /**
     * 设置当前音频振幅
     * @param amplitude 振幅值 (0-100)
     */
    public void setAmplitude(float amplitude) {
        // 限制振幅范围
        amplitude = Math.max(0, Math.min(MAX_AMPLITUDE, amplitude));
        this.currentAmplitude = amplitude;
        
        // 添加到历史记录
        amplitudeHistory.add(amplitude);
        
        // 保持历史记录大小
        if (amplitudeHistory.size() > MAX_AMPLITUDE_HISTORY) {
            amplitudeHistory.remove(0);
        }
        
        // 触发重绘
        invalidate();
    }
    
    /**
     * 开始动画
     */
    public void startAnimation() {
        isAnimating = true;
        lastUpdateTime = System.currentTimeMillis();
        startSimulatedWaveform();
    }
    
    /**
     * 停止动画
     */
    public void stopAnimation() {
        isAnimating = false;
        // 逐渐减少振幅到0
        fadeOutWaveform();
    }
    
    /**
     * 模拟波形动画（当没有实际音频输入时）
     */
    private void startSimulatedWaveform() {
        if (!isAnimating) {
            return;
        }
        
        post(new Runnable() {
            @Override
            public void run() {
                if (isAnimating) {
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastUpdateTime >= UPDATE_INTERVAL) {
                        // 生成模拟的振幅值
                        Random random = new Random();
                        float simulatedAmplitude = DEFAULT_AMPLITUDE + random.nextFloat() * 30;
                        setAmplitude(simulatedAmplitude);
                        lastUpdateTime = currentTime;
                    }
                    
                    // 继续动画
                    postDelayed(this, 16); // ~60 FPS
                }
            }
        });
    }
    
    /**
     * 淡出波形动画
     */
    private void fadeOutWaveform() {
        post(new Runnable() {
            @Override
            public void run() {
                boolean hasNonZeroAmplitude = false;
                
                // 逐渐减少所有振幅值
                for (int i = 0; i < amplitudeHistory.size(); i++) {
                    float currentAmp = amplitudeHistory.get(i);
                    if (currentAmp > 0) {
                        float newAmp = Math.max(0, currentAmp - 2f);
                        amplitudeHistory.set(i, newAmp);
                        if (newAmp > 0) {
                            hasNonZeroAmplitude = true;
                        }
                    }
                }
                
                invalidate();
                
                // 如果还有非零振幅，继续淡出
                if (hasNonZeroAmplitude) {
                    postDelayed(this, 50);
                }
            }
        });
    }
    
    /**
     * 设置波形颜色
     */
    public void setWaveformColor(int color) {
        this.waveformColor = color;
        waveformPaint.setColor(color);
        invalidate();
    }
    
    /**
     * 清除波形历史
     */
    public void clearWaveform() {
        amplitudeHistory.clear();
        for (int i = 0; i < MAX_AMPLITUDE_HISTORY; i++) {
            amplitudeHistory.add(0f);
        }
        invalidate();
    }
}
