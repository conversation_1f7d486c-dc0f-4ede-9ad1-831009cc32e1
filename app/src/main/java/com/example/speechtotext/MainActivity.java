package com.example.speechtotext;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

public class MainActivity extends AppCompatActivity implements AzureSpeech.AzureSpeechListener {
    private static final int REQUEST_RECORD_AUDIO_PERMISSION = 200;
    private TextView textView;
    private Button button;
    private AzureSpeech azureSpeech;
    private boolean islistening=false;



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setContentView(R.layout.activity_main);
        textView = findViewById(R.id.textViewResult);
        button = findViewById(R.id.buttonStart);
        azureSpeech=new AzureSpeech(this);
        button.setOnClickListener(view ->{ToggleListening();} );

    }

//    private void StartListening() {
//        if (checkAudioPermission()) {
//            azureSpeech.StartListening();
//        } else {
//            requestAudioPermissions();
//        }
//    }

    private boolean checkAudioPermission() {
        return checkSelfPermission(Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
    }
    private void requestAudioPermissions() {
        requestPermissions(new String[]{Manifest.permission.RECORD_AUDIO}, REQUEST_RECORD_AUDIO_PERMISSION);
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION && grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            azureSpeech.StartListening();
        } else {
            Toast.makeText(this, "Permission Denied", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        azureSpeech.Dispose();
    }

    @Override
    protected void onResume() {
        super.onResume();
        azureSpeech.ResumeListening();
    }

    @Override
    protected void onPause() {
        super.onPause();
        azureSpeech.PauseListening();
    }

    private void ToggleListening(){
    if(islistening){
        azureSpeech.StopListening();
        button.setText("Start Speech Recognition");
        islistening=false;
    }else{
        if (checkAudioPermission()) {
            button.setText("Stop Speech Recognition");
            azureSpeech.StartListening();
            islistening = true;
        } else {
            requestAudioPermissions();
        }
    }
    }

    @Override
    public void onUpdateText(final String text) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                textView.setText(text+"\n");
                // Scroll to the bottom
                final int scrollAmount = textView.getLayout().getLineTop(textView.getLineCount()) - textView.getHeight();
                if (scrollAmount > 0)
                    textView.scrollTo(0, scrollAmount);
                else
                    textView.scrollTo(0, 0);
            }
        });
    }


}