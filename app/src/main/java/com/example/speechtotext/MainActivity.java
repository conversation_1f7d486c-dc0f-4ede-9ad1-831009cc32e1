package com.example.speechtotext;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

public class MainActivity extends AppCompatActivity
        implements AzureSpeech.AzureSpeechListener, SpeechResultManager.SpeechResultListener,
        AudioLevelMonitor.AudioLevelListener {

    private static final String TAG = "MainActivity";
    private static final int REQUEST_RECORD_AUDIO_PERMISSION = 200;

    // UI组件
    private RecyclerView recyclerViewResults;
    private FloatingActionButton fabMicrophone;
    private FrameLayout waveformContainer;
    private TextView textAudioLevel;
    private AudioWaveformView audioWaveformView;

    // 核心组件
    private AzureSpeech azureSpeech;
    private SpeechResultManager resultManager;
    private RecognitionResultAdapter adapter;
    private AudioLevelMonitor audioLevelMonitor;

    // 状态管理
    private boolean isListening = false;



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setContentView(R.layout.activity_main);

        initializeViews();
        initializeComponents();
        setupRecyclerView();
        setupClickListeners();

        Log.d(TAG, "MainActivity created successfully");
    }

    /**
     * 初始化UI组件
     */
    private void initializeViews() {
        recyclerViewResults = findViewById(R.id.recycler_view_results);
        fabMicrophone = findViewById(R.id.fab_microphone);
        waveformContainer = findViewById(R.id.waveform_container);
        textAudioLevel = findViewById(R.id.text_audio_level);
        audioWaveformView = findViewById(R.id.audio_waveform_view);
    }

    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        // 初始化结果管理器
        resultManager = new SpeechResultManager();
        resultManager.setListener(this);

        // 初始化适配器
        adapter = new RecognitionResultAdapter();

        // 初始化语音识别服务
        azureSpeech = new AzureSpeech(this);

        // 初始化音频级别监测器
        audioLevelMonitor = new AudioLevelMonitor();
        audioLevelMonitor.setAudioLevelListener(this);

        Log.d(TAG, "Core components initialized");
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setStackFromEnd(true); // 从底部开始堆叠，新项目显示在底部
        recyclerViewResults.setLayoutManager(layoutManager);
        recyclerViewResults.setAdapter(adapter);

        // 优化RecyclerView性能
        recyclerViewResults.setHasFixedSize(false); // 内容大小可变
        recyclerViewResults.setItemViewCacheSize(20); // 增加缓存大小

        Log.d(TAG, "RecyclerView setup completed");
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        fabMicrophone.setOnClickListener(view -> toggleListening());
    }

    /**
     * 切换语音识别状态
     */
    private void toggleListening() {
        if (isListening) {
            stopListening();
        } else {
            startListening();
        }
    }

    /**
     * 开始语音识别
     */
    private void startListening() {
        if (checkAudioPermission()) {
            // 启动语音识别
            azureSpeech.StartListening();

            // 启动音频级别监测
            if (audioLevelMonitor != null) {
                audioLevelMonitor.startMonitoring();
                // Log audio enhancement status
                if (audioLevelMonitor.areAudioEnhancementsEnabled()) {
                    Log.i(TAG, "Audio enhancements are active");
                    Log.d(TAG, audioLevelMonitor.getAudioEnhancementStatus());
                } else {
                    Log.w(TAG, "Audio enhancements are not available on this device");
                }
            }

            // 启动波形图动画
            if (audioWaveformView != null) {
                audioWaveformView.startAnimation();
            }

            isListening = true;
            updateUIForListening(true);
            Log.d(TAG, "Started listening with audio monitoring");
        } else {
            requestAudioPermissions();
        }
    }

    /**
     * 停止语音识别
     */
    private void stopListening() {
        // 停止语音识别
        azureSpeech.StopListening();

        // 停止音频级别监测
        if (audioLevelMonitor != null) {
            audioLevelMonitor.stopMonitoring();
        }

        // 停止波形图动画
        if (audioWaveformView != null) {
            audioWaveformView.stopAnimation();
        }

        isListening = false;
        updateUIForListening(false);
        Log.d(TAG, "Stopped listening and audio monitoring");
    }

    /**
     * 更新UI以反映语音识别状态
     */
    private void updateUIForListening(boolean listening) {
        if (listening) {
            // 显示波形图容器
            waveformContainer.setVisibility(View.VISIBLE);
            // 更改按钮颜色为录音状态（红色）
            fabMicrophone.setBackgroundTintList(
                    getResources().getColorStateList(android.R.color.holo_red_dark, getTheme()));
            Log.d(TAG, "UI updated for listening state");
        } else {
            // 隐藏波形图容器
            waveformContainer.setVisibility(View.GONE);
            // 恢复按钮颜色为空闲状态（绿色）
            fabMicrophone.setBackgroundTintList(
                    getResources().getColorStateList(android.R.color.holo_green_dark, getTheme()));
            Log.d(TAG, "UI updated for idle state");
        }
    }

    /**
     * 检查录音权限
     */
    private boolean checkAudioPermission() {
        return checkSelfPermission(Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 请求录音权限
     */
    private void requestAudioPermissions() {
        requestPermissions(new String[]{Manifest.permission.RECORD_AUDIO}, REQUEST_RECORD_AUDIO_PERMISSION);
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startListening();
                Toast.makeText(this, "录音权限已授予", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "需要录音权限才能使用语音识别功能", Toast.LENGTH_LONG).show();
                Log.w(TAG, "Audio permission denied");
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理语音识别服务
        if (azureSpeech != null) {
            azureSpeech.Dispose();
        }

        // 清理结果管理器
        if (resultManager != null) {
            resultManager.setListener(null);
        }

        // 清理音频监测器
        if (audioLevelMonitor != null) {
            audioLevelMonitor.release();
        }

        Log.d(TAG, "MainActivity destroyed, all resources cleaned up");
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (azureSpeech != null) {
            azureSpeech.ResumeListening();
        }
        Log.d(TAG, "MainActivity resumed");
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (azureSpeech != null) {
            azureSpeech.PauseListening();
        }
        Log.d(TAG, "MainActivity paused");
    }

    // ==================== AzureSpeech.AzureSpeechListener 实现 ====================

    @Override
    public void onUpdateText(final String text) {
        runOnUiThread(() -> {
            if (text != null && !text.trim().isEmpty()) {
                // 添加部分识别结果
                resultManager.addPartialResult(text);
                Log.d(TAG, "Received partial text: " + text.substring(0, Math.min(text.length(), 50)) + "...");
            }
        });
    }

    @Override
    public void onFinalText(final String text) {
        runOnUiThread(() -> {
            if (text != null && !text.trim().isEmpty()) {
                // 确认为最终结果
                resultManager.confirmPartialResult(text);
                Log.d(TAG, "Received final text: " + text.substring(0, Math.min(text.length(), 50)) + "...");
            }
        });
    }

    @Override
    public void onError(String error) {
        runOnUiThread(() -> {
            Toast.makeText(this, "语音识别错误: " + error, Toast.LENGTH_LONG).show();
            Log.e(TAG, "Speech recognition error: " + error);
            // 如果发生错误，停止监听
            if (isListening) {
                stopListening();
            }
        });
    }

    // ==================== SpeechResultManager.SpeechResultListener 实现 ====================

    @Override
    public void onResultAdded(SpeechResultItem item, int position) {
        runOnUiThread(() -> {
            adapter.addResult(item);
            scrollToBottom();
            Log.d(TAG, "Result added at position: " + position);
        });
    }

    @Override
    public void onResultUpdated(SpeechResultItem item, int position) {
        runOnUiThread(() -> {
            adapter.updateResult(position, item);
            Log.d(TAG, "Result updated at position: " + position);
        });
    }

    @Override
    public void onResultsCleared() {
        runOnUiThread(() -> {
            adapter.clearResults();
            Log.d(TAG, "All results cleared");
        });
    }

    // ==================== AudioLevelMonitor.AudioLevelListener 实现 ====================

    @Override
    public void onAudioLevelChanged(float level) {
        runOnUiThread(() -> {
            // 更新波形图
            if (audioWaveformView != null) {
                audioWaveformView.setAmplitude(level);
            }

            // 更新音频级别文本（可选）
            if (textAudioLevel != null) {
                textAudioLevel.setText(String.format("Audio Level: %.1f", level));
            }
        });
    }

    // ==================== 辅助方法 ====================

    /**
     * 滚动到RecyclerView底部
     */
    private void scrollToBottom() {
        if (adapter.getItemCount() > 0) {
            recyclerViewResults.smoothScrollToPosition(adapter.getItemCount() - 1);
        }
    }

    /**
     * 获取内存使用统计信息（用于调试）
     */
    private void logMemoryStats() {
        if (resultManager != null) {
            Log.d(TAG, "Memory stats: " + resultManager.getMemoryStats());
        }
    }
}