package com.example.speechtotext;

import android.util.Log;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 语音识别结果管理器
 * 负责管理识别结果的内存缓存，支持长文本高效处理
 */
public class SpeechResultManager {
    private static final String TAG = "SpeechResultManager";
    private static final int MAX_ITEMS = 1000; // 最大缓存项目数，防止内存泄漏
    private static final int MAX_TEXT_LENGTH_PER_ITEM = 500; // 单个项目最大文本长度
    
    private final CopyOnWriteArrayList<SpeechResultItem> resultItems;
    private int nextId = 0;
    private SpeechResultListener listener;
    
    // 当前正在识别的部分结果ID，用于更新实时识别文本
    private int currentPartialResultId = -1;

    public interface SpeechResultListener {
        void onResultAdded(SpeechResultItem item, int position);
        void onResultUpdated(SpeechResultItem item, int position);
        void onResultsCleared();
    }

    public SpeechResultManager() {
        this.resultItems = new CopyOnWriteArrayList<>();
    }

    public void setListener(SpeechResultListener listener) {
        this.listener = listener;
    }

    /**
     * 添加部分识别结果（实时识别中）
     */
    public synchronized void addPartialResult(String text) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }

        long timestamp = System.currentTimeMillis();
        
        // 如果已有部分结果，更新它
        if (currentPartialResultId != -1) {
            updatePartialResult(text);
            return;
        }

        // 创建新的部分结果
        SpeechResultItem item = new SpeechResultItem(nextId++, text, timestamp, true);
        currentPartialResultId = item.getId();
        
        // 检查内存限制
        checkMemoryLimit();
        
        resultItems.add(item);
        
        if (listener != null) {
            listener.onResultAdded(item, resultItems.size() - 1);
        }
        
        Log.d(TAG, "Added partial result: " + text.substring(0, Math.min(text.length(), 50)) + "...");
    }

    /**
     * 更新当前的部分识别结果
     */
    private synchronized void updatePartialResult(String text) {
        if (currentPartialResultId == -1) {
            return;
        }

        for (int i = resultItems.size() - 1; i >= 0; i--) {
            SpeechResultItem item = resultItems.get(i);
            if (item.getId() == currentPartialResultId && item.isPartial()) {
                item.setText(text);
                item.setTimestamp(System.currentTimeMillis());
                
                if (listener != null) {
                    listener.onResultUpdated(item, i);
                }
                break;
            }
        }
    }

    /**
     * 确认部分结果为最终结果
     */
    public synchronized void confirmPartialResult(String finalText) {
        if (currentPartialResultId == -1) {
            // 没有部分结果，直接添加最终结果
            addFinalResult(finalText);
            return;
        }

        for (int i = resultItems.size() - 1; i >= 0; i--) {
            SpeechResultItem item = resultItems.get(i);
            if (item.getId() == currentPartialResultId && item.isPartial()) {
                item.setText(finalText);
                item.setPartial(false);
                item.setTimestamp(System.currentTimeMillis());
                
                if (listener != null) {
                    listener.onResultUpdated(item, i);
                }
                break;
            }
        }
        
        currentPartialResultId = -1;
        Log.d(TAG, "Confirmed final result: " + finalText.substring(0, Math.min(finalText.length(), 50)) + "...");
    }

    /**
     * 添加最终识别结果
     */
    public synchronized void addFinalResult(String text) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }

        long timestamp = System.currentTimeMillis();
        SpeechResultItem item = new SpeechResultItem(nextId++, text, timestamp, false);
        
        // 检查内存限制
        checkMemoryLimit();
        
        resultItems.add(item);
        
        if (listener != null) {
            listener.onResultAdded(item, resultItems.size() - 1);
        }
        
        Log.d(TAG, "Added final result: " + text.substring(0, Math.min(text.length(), 50)) + "...");
    }

    /**
     * 检查内存限制，移除过旧的项目
     */
    private void checkMemoryLimit() {
        while (resultItems.size() >= MAX_ITEMS) {
            resultItems.remove(0);
            Log.d(TAG, "Removed old result item to maintain memory limit");
        }
    }

    /**
     * 清除所有结果
     */
    public synchronized void clearResults() {
        resultItems.clear();
        currentPartialResultId = -1;
        nextId = 0;
        
        if (listener != null) {
            listener.onResultsCleared();
        }
        
        Log.d(TAG, "Cleared all results");
    }

    /**
     * 获取所有结果的副本
     */
    public List<SpeechResultItem> getAllResults() {
        return new ArrayList<>(resultItems);
    }

    /**
     * 获取结果总数
     */
    public int getResultCount() {
        return resultItems.size();
    }

    /**
     * 获取指定位置的结果
     */
    public SpeechResultItem getResult(int position) {
        if (position >= 0 && position < resultItems.size()) {
            return resultItems.get(position);
        }
        return null;
    }

    /**
     * 获取内存使用统计信息
     */
    public String getMemoryStats() {
        int totalTextLength = 0;
        for (SpeechResultItem item : resultItems) {
            if (item.getText() != null) {
                totalTextLength += item.getText().length();
            }
        }
        
        return String.format("Items: %d, Total text length: %d, Memory usage: ~%d KB", 
                resultItems.size(), totalTextLength, (totalTextLength * 2) / 1024);
    }
}
