package com.example.speechtotext;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 语音识别结果RecyclerView适配器
 * 使用DiffUtil优化性能，支持长文本高效显示
 */
public class RecognitionResultAdapter extends RecyclerView.Adapter<RecognitionResultAdapter.ViewHolder> {
    
    private List<SpeechResultItem> resultItems = new ArrayList<>();
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());

    /**
     * ViewHolder类，使用ViewHolder模式优化性能
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView textContent;
        public TextView textTimestamp;
        public View itemView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            this.itemView = itemView;
            textContent = itemView.findViewById(R.id.text_content);
            textTimestamp = itemView.findViewById(R.id.text_timestamp);
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_speech_result, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SpeechResultItem item = resultItems.get(position);
        
        // 设置文本内容
        holder.textContent.setText(item.getText());
        
        // 设置时间戳
        String timeStr = timeFormat.format(new Date(item.getTimestamp()));
        holder.textTimestamp.setText(timeStr);
        
        // 根据是否为部分结果设置不同的样式
        if (item.isPartial()) {
            // 部分结果使用半透明样式
            holder.textContent.setAlpha(0.7f);
            holder.textTimestamp.setAlpha(0.7f);
            holder.itemView.setBackgroundResource(R.drawable.bg_partial_result);
        } else {
            // 最终结果使用正常样式
            holder.textContent.setAlpha(1.0f);
            holder.textTimestamp.setAlpha(1.0f);
            holder.itemView.setBackgroundResource(R.drawable.bg_final_result);
        }
    }

    @Override
    public int getItemCount() {
        return resultItems.size();
    }

    /**
     * 使用DiffUtil更新数据，优化性能
     */
    public void updateResults(List<SpeechResultItem> newResults) {
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(
                new SpeechResultDiffCallback(this.resultItems, newResults));
        
        this.resultItems.clear();
        this.resultItems.addAll(newResults);
        
        diffResult.dispatchUpdatesTo(this);
    }

    /**
     * 添加单个结果项
     */
    public void addResult(SpeechResultItem item) {
        resultItems.add(item);
        notifyItemInserted(resultItems.size() - 1);
    }

    /**
     * 更新指定位置的结果项
     */
    public void updateResult(int position, SpeechResultItem item) {
        if (position >= 0 && position < resultItems.size()) {
            resultItems.set(position, item);
            notifyItemChanged(position);
        }
    }

    /**
     * 清除所有结果
     */
    public void clearResults() {
        int size = resultItems.size();
        resultItems.clear();
        notifyItemRangeRemoved(0, size);
    }

    /**
     * DiffUtil回调类，用于高效计算列表差异
     */
    private static class SpeechResultDiffCallback extends DiffUtil.Callback {
        private final List<SpeechResultItem> oldList;
        private final List<SpeechResultItem> newList;

        public SpeechResultDiffCallback(List<SpeechResultItem> oldList, List<SpeechResultItem> newList) {
            this.oldList = oldList;
            this.newList = newList;
        }

        @Override
        public int getOldListSize() {
            return oldList.size();
        }

        @Override
        public int getNewListSize() {
            return newList.size();
        }

        @Override
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            return oldList.get(oldItemPosition).getId() == newList.get(newItemPosition).getId();
        }

        @Override
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            SpeechResultItem oldItem = oldList.get(oldItemPosition);
            SpeechResultItem newItem = newList.get(newItemPosition);
            return oldItem.equals(newItem);
        }
    }

    /**
     * 获取当前数据的副本
     */
    public List<SpeechResultItem> getCurrentResults() {
        return new ArrayList<>(resultItems);
    }

    /**
     * 获取最后一个结果项
     */
    public SpeechResultItem getLastResult() {
        if (!resultItems.isEmpty()) {
            return resultItems.get(resultItems.size() - 1);
        }
        return null;
    }
}
