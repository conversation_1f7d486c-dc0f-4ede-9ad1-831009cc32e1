package com.example.speechtotext;
import android.util.Log;

import com.microsoft.cognitiveservices.speech.*;
public class AzureSpeech {
    private static String speechSubscriptionKey = "767ddc9821bc4c868eab093c2c8b080c";
    private static String serviceRegion = "southeastasia";
    private SpeechRecognizer recognizer=null;
    public boolean isListening = false;
    private AzureSpeechListener listener;

    public  AzureSpeech(AzureSpeechListener listener){
        try {
            // Initialize SpeechConfig
            SpeechConfig config = SpeechConfig.fromSubscription(speechSubscriptionKey, serviceRegion);
            config.setSpeechRecognitionLanguage("zh-HK");
            // Create a speech recognizer
            this.recognizer = new SpeechRecognizer(config);

            // Subscribe to events
            recognizer.recognizing.addEventListener((s, e) -> {
//                System.out.println("RECOGNIZING: Text=" + e.getResult().getText());
                if (this.listener != null) {
                    this.listener.onUpdateText(e.getResult().getText());
                }
                Log.i("check","RECOGNIZING: Text=" + e.getResult().getText());
            });

            recognizer.recognized.addEventListener((s, e) -> {
                if (e.getResult().getReason() == ResultReason.RecognizedSpeech) {
                    if (this.listener != null) {
                        this.listener.onUpdateText(e.getResult().getText());
                    }
                    Log.i("check","RECOGNIZING: Text=" + e.getResult().getText());
                }
                else if (e.getResult().getReason() == ResultReason.NoMatch) {
                    Log.i("check","NOMATCH: Speech could not be recognized.");
                }
            });

            recognizer.canceled.addEventListener((s, e) -> {
                Log.i("check","CANCELED: Reason=" + e.getReason());
                if (e.getReason() == CancellationReason.Error) {
                    Log.i("check","CANCELED: ErrorCode=" + e.getErrorCode());
                    Log.i("check","CANCELED: ErrorDetails=" + e.getErrorDetails());
                }
            });

            // Keep the app running
            // You can implement a mechanism to stop the recognition as needed
        } catch (Exception ex) {
            Log.i("check","Error: " + ex.getMessage());
        }
        this.listener = listener;
    }

    public void StartListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized1");
            return;
        }
        this.recognizer.startContinuousRecognitionAsync();
        this.isListening=true;
    }

    public void StopListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized");
            return;
        }
        this.recognizer.stopContinuousRecognitionAsync();
        this.isListening = false;
    }

    public void PauseListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized");
            return;
        }
        if(this.isListening){
            this.recognizer.stopContinuousRecognitionAsync();
        }
    }

    public void ResumeListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized");
            return;
        }
        if(this.isListening){
        this.recognizer.startContinuousRecognitionAsync();
        }
    }

    public void Dispose() {
        if (this.recognizer != null) {
            if (isListening) {
                this.recognizer.stopContinuousRecognitionAsync();
            }
            this.recognizer.close();
        }
    }

    public interface AzureSpeechListener {
        void onUpdateText(String text);
    }

}
