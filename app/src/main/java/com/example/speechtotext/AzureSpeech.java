package com.example.speechtotext;
import android.util.Log;

import com.microsoft.cognitiveservices.speech.*;
public class AzureSpeech {
    private static String speechSubscriptionKey = "767ddc9821bc4c868eab093c2c8b080c";
    private static String serviceRegion = "southeastasia";
    private SpeechRecognizer recognizer=null;
    public boolean isListening = false;
    private AzureSpeechListener listener;

    public  AzureSpeech(AzureSpeechListener listener){
        try {
            // Initialize SpeechConfig
            SpeechConfig config = SpeechConfig.fromSubscription(speechSubscriptionKey, serviceRegion);
            config.setSpeechRecognitionLanguage("zh-HK");
            // Create a speech recognizer
            this.recognizer = new SpeechRecognizer(config);

            // Subscribe to events
            // 部分识别结果（实时识别中）
            recognizer.recognizing.addEventListener((s, e) -> {
                String text = e.getResult().getText();
                if (this.listener != null && text != null && !text.trim().isEmpty()) {
                    this.listener.onUpdateText(text);
                }
                Log.d("AzureSpeech", "RECOGNIZING: " + text);
            });

            // 最终识别结果
            recognizer.recognized.addEventListener((s, e) -> {
                if (e.getResult().getReason() == ResultReason.RecognizedSpeech) {
                    String text = e.getResult().getText();
                    if (this.listener != null && text != null && !text.trim().isEmpty()) {
                        // 调用最终结果回调
                        this.listener.onFinalText(text);
                    }
                    Log.d("AzureSpeech", "RECOGNIZED: " + text);
                }
                else if (e.getResult().getReason() == ResultReason.NoMatch) {
                    Log.w("AzureSpeech", "NOMATCH: Speech could not be recognized.");
                }
            });

            // 识别取消或错误
            recognizer.canceled.addEventListener((s, e) -> {
                Log.w("AzureSpeech", "CANCELED: Reason=" + e.getReason());
                if (e.getReason() == CancellationReason.Error) {
                    String errorMsg = "ErrorCode=" + e.getErrorCode() + ", ErrorDetails=" + e.getErrorDetails();
                    Log.e("AzureSpeech", "CANCELED: " + errorMsg);
                    if (this.listener != null) {
                        this.listener.onError(errorMsg);
                    }
                }
            });

            // Keep the app running
            // You can implement a mechanism to stop the recognition as needed
        } catch (Exception ex) {
            Log.i("check","Error: " + ex.getMessage());
        }
        this.listener = listener;
    }

    public void StartListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized1");
            return;
        }
        this.recognizer.startContinuousRecognitionAsync();
        this.isListening=true;
    }

    public void StopListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized");
            return;
        }
        this.recognizer.stopContinuousRecognitionAsync();
        this.isListening = false;
    }

    public void PauseListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized");
            return;
        }
        if(this.isListening){
            this.recognizer.stopContinuousRecognitionAsync();
        }
    }

    public void ResumeListening(){
        if(this.recognizer==null){
            Log.e("check","Haven't initialized");
            return;
        }
        if(this.isListening){
        this.recognizer.startContinuousRecognitionAsync();
        }
    }

    public void Dispose() {
        if (this.recognizer != null) {
            if (isListening) {
                this.recognizer.stopContinuousRecognitionAsync();
            }
            this.recognizer.close();
        }
    }

    public interface AzureSpeechListener {
        void onUpdateText(String text); // 部分识别结果（实时）

        // 可选：添加最终结果回调（如果需要区分）
        default void onFinalText(String text) {
            onUpdateText(text);
        }

        // 可选：添加错误回调
        default void onError(String error) {
            // 默认空实现
        }
    }

}
