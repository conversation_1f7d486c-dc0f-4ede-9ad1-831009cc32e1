package com.example.speechtotext;
import android.util.Log;
import java.util.concurrent.Future;

import com.microsoft.cognitiveservices.speech.*;
public class AzureSpeech {
    private static final String TAG = "AzureSpeech";
    private static String speechSubscriptionKey = "767ddc9821bc4c868eab093c2c8b080c";
    private static String serviceRegion = "southeastasia";
    private SpeechRecognizer recognizer = null;
    private SpeechConfig config = null;
    public boolean isListening = false;
    private boolean isInitialized = false;
    private AzureSpeechListener listener;

    public AzureSpeech(AzureSpeechListener listener) {
        this.listener = listener;
        initializeRecognizer();
    }

    private void initializeRecognizer() {
        try {
            // Initialize SpeechConfig
            config = SpeechConfig.fromSubscription(speechSubscriptionKey, serviceRegion);
            config.setSpeechRecognitionLanguage("zh-HK");

            // Create a speech recognizer
            this.recognizer = new SpeechRecognizer(config);

            setupEventListeners();
            isInitialized = true;
            Log.d(TAG, "Speech recognizer initialized successfully");

        } catch (Exception ex) {
            Log.e(TAG, "Error initializing recognizer: " + ex.getMessage());
            isInitialized = false;
        }
    }

    private void setupEventListeners() {
        if (recognizer == null) return;

        // 部分识别结果（实时识别中）
        recognizer.recognizing.addEventListener((s, e) -> {
            String text = e.getResult().getText();
            if (this.listener != null && text != null && !text.trim().isEmpty()) {
                this.listener.onUpdateText(text);
            }
            Log.d(TAG, "RECOGNIZING: " + text);
        });

        // 最终识别结果
        recognizer.recognized.addEventListener((s, e) -> {
            if (e.getResult().getReason() == ResultReason.RecognizedSpeech) {
                String text = e.getResult().getText();
                if (this.listener != null && text != null && !text.trim().isEmpty()) {
                    this.listener.onFinalText(text);
                }
                Log.d(TAG, "RECOGNIZED: " + text);
            } else if (e.getResult().getReason() == ResultReason.NoMatch) {
                Log.w(TAG, "NOMATCH: Speech could not be recognized.");
            }
        });

        // 识别取消或错误
        recognizer.canceled.addEventListener((s, e) -> {
            Log.w(TAG, "CANCELED: Reason=" + e.getReason());
            isListening = false;

            if (e.getReason() == CancellationReason.Error) {
                String errorMsg = "ErrorCode=" + e.getErrorCode() + ", ErrorDetails=" + e.getErrorDetails();
                Log.e(TAG, "CANCELED: " + errorMsg);
                if (this.listener != null) {
                    this.listener.onError(errorMsg);
                }
                // Reset recognizer on error
                resetRecognizer();
            }
        });

        // Session stopped event
        recognizer.sessionStopped.addEventListener((s, e) -> {
            Log.d(TAG, "Session stopped");
            isListening = false;
        });
    }

    public void StartListening() {
        if (!isInitialized || recognizer == null) {
            Log.e(TAG, "Recognizer not initialized, attempting to reinitialize");
            initializeRecognizer();
            if (!isInitialized) {
                Log.e(TAG, "Failed to initialize recognizer");
                return;
            }
        }

        if (isListening) {
            Log.w(TAG, "Already listening, ignoring start request");
            return;
        }

        // Run async operation in background thread
        new Thread(() -> {
            try {
                Log.d(TAG, "Starting continuous recognition");
                Future<Void> task = recognizer.startContinuousRecognitionAsync();

                // Wait for the start operation to complete
                task.get();
                isListening = true;
                Log.d(TAG, "Recognition started successfully");

            } catch (Exception e) {
                Log.e(TAG, "Error starting recognition: " + e.getMessage());
                isListening = false;
                if (listener != null) {
                    listener.onError("Failed to start recognition: " + e.getMessage());
                }
            }
        }).start();
    }

    public void StopListening() {
        if (!isListening || recognizer == null) {
            Log.w(TAG, "Not currently listening, ignoring stop request");
            isListening = false;
            return;
        }

        // Run async operation in background thread
        new Thread(() -> {
            try {
                Log.d(TAG, "Stopping continuous recognition");
                Future<Void> task = recognizer.stopContinuousRecognitionAsync();

                // Wait for the stop operation to complete
                task.get();
                isListening = false;
                Log.d(TAG, "Recognition stopped successfully");

            } catch (Exception e) {
                Log.e(TAG, "Error stopping recognition: " + e.getMessage());
                isListening = false;
                // Force reset on error
                resetRecognizer();
            }
        }).start();
    }

    private void resetRecognizer() {
        Log.d(TAG, "Resetting recognizer");
        try {
            if (recognizer != null) {
                recognizer.close();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error closing recognizer: " + e.getMessage());
        }

        recognizer = null;
        isInitialized = false;
        isListening = false;

        // Reinitialize
        initializeRecognizer();
    }

    public void PauseListening() {
        if (!isInitialized || recognizer == null) {
            Log.w(TAG, "Recognizer not initialized");
            return;
        }

        if (isListening) {
            StopListening();
        }
    }

    public void ResumeListening() {
        if (!isInitialized || recognizer == null) {
            Log.w(TAG, "Recognizer not initialized");
            return;
        }

        if (!isListening) {
            StartListening();
        }
    }

    public void Dispose() {
        Log.d(TAG, "Disposing recognizer");

        // Run dispose in background thread
        new Thread(() -> {
            if (isListening && recognizer != null) {
                try {
                    Future<Void> task = recognizer.stopContinuousRecognitionAsync();
                    task.get(); // Wait for stop to complete
                } catch (Exception e) {
                    Log.e(TAG, "Error stopping recognition during dispose: " + e.getMessage());
                }
            }

            if (recognizer != null) {
                try {
                    recognizer.close();
                } catch (Exception e) {
                    Log.e(TAG, "Error closing recognizer: " + e.getMessage());
                }
                recognizer = null;
            }

            isListening = false;
            isInitialized = false;
            Log.d(TAG, "Recognizer disposed successfully");
        }).start();
    }

    public interface AzureSpeechListener {
        void onUpdateText(String text); // 部分识别结果（实时）

        // 可选：添加最终结果回调（如果需要区分）
        default void onFinalText(String text) {
            onUpdateText(text);
        }

        // 可选：添加错误回调
        default void onError(String error) {
            // 默认空实现
        }
    }

}
