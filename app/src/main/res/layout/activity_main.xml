<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/transparent"
    tools:context=".MainActivity">

    <!-- 顶部：音频波形图显示区域 -->
    <FrameLayout
        android:id="@+id/waveform_container"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_margin="16dp"
        android:background="@drawable/bg_waveform_container"
        android:visibility="gone">

        <TextView
            android:id="@+id/text_audio_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|start"
            android:layout_margin="12dp"
            android:text="Audio Level"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <!-- 音频波形图视图 -->
        <com.example.speechtotext.AudioWaveformView
            android:id="@+id/audio_waveform_view"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:layout_marginHorizontal="16dp" />

    </FrameLayout>

    <!-- 中间：RecyclerView文本显示区域 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_results"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="8dp"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="16dp"
        android:scrollbars="vertical"
        android:overScrollMode="always" />

    <!-- 底部：圆形麦克风录音按钮 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:layout_marginTop="16dp">

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_microphone"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_microphone"
            app:backgroundTint="@android:color/holo_green_dark"
            app:tint="#FFFFFF"
            app:elevation="8dp"
            app:fabSize="auto"
            app:borderWidth="0dp" />

    </FrameLayout>

</LinearLayout>
