<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:context=".MainActivity">

    <Button
        android:id="@+id/buttonStart"
        android:layout_width="361dp"
        android:layout_height="111dp"
        android:text="Start Speech Recognition"
        android:textSize="36sp"
        app:layout_constraintBottom_toTopOf="@+id/textViewResult"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/textViewResult"
        android:layout_width="410dp"
        android:layout_height="534dp"
        android:layout_marginTop="24dp"
        android:background="@android:color/transparent"
        android:padding="16dp"
        android:text="Recognition results will appear here."
        android:textColor="#FFFFFF"
        android:textSize="44sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/buttonStart"
        android:scrollbars="vertical"
        android:overScrollMode="always"/>

</androidx.constraintlayout.widget.ConstraintLayout>
