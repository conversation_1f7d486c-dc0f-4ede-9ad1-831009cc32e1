<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:layout_marginVertical="4dp"
    android:layout_marginHorizontal="8dp"
    android:background="@drawable/bg_final_result">

    <TextView
        android:id="@+id/text_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:lineSpacingExtra="4dp"
        android:text="识别结果文本将显示在这里"
        android:textIsSelectable="true" />

    <TextView
        android:id="@+id/text_timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="#CCCCCC"
        android:textSize="12sp"
        android:text="00:00:00"
        android:layout_gravity="end" />

</LinearLayout>
