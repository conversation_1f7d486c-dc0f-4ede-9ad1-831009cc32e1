plugins {
    id 'com.android.application'
}

android {
    namespace 'com.example.speechtotext'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.speechtotext"
        minSdkVersion 28
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    packagingOptions {
        resources {
            excludes += ['META-INF/INDEX.LIST', 'META-INF/DEPENDENCIES']
        }
    }


    buildTypes {
        release {
            minifyEnabled false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
//    kotlinOptions {
//        jvmTarget = "1.8"
//    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation 'androidx.core:core-ktx:1.12.0'
    // https://mvnrepository.com/artifact/com.google.cloud/google-cloud-speech
//    implementation 'com.google.cloud:google-cloud-speech:1.29.1'
//    implementation 'com.google.auth:google-auth-library-oauth2-http:0.26.0'
//    implementation "io.grpc:grpc-okhttp:1.38.1"
//    implementation "io.grpc:grpc-stub:1.38.1"
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.33.0'
}
